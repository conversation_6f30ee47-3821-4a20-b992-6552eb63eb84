"use strict";
import "../../common/js/common_1v1.js";
import "../../common/js/lottie.js";
import "../../common/js/teleprompter.js";
// import "../../common/template/dialog/index.js";
import "../../common/template/multyDialog/index.js";
import {getMemory} from "../../common/template/memory";

const isSync =
  parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;
import { resultHide } from "../../common/template/ribbonWin/index.js";
import {
  USER_TYPE,
  TEACHER_TYPE,
  INTERACTION_TYPE,
  CLASS_STATUS,
  USERACTION_TYPE,
} from "../../common/js/constants.js";

$(function () {
  let staticData = configData.source; // 获取配置数据
  let hasPractice = staticData.hasPractice
    ? String(staticData.hasPractice)
    : "1";
  let memoryData = getMemory();
  //是否在控制器显示功能按钮
  window.h5Template = {
    hasDemo: "0", //0 默认值，无提示功能  1 有提示功能
    hasPractice: '1', //0 无授权功能  1  默认值，普通授权模式  2 start授权模式
  };

  let gameList = staticData.gameList; //每轮数据
  gameList.forEach((item) => (item.finishCount = 0));
  let modleList = []; //每轮数据
  let userType =
    window.frameElement && window.frameElement.getAttribute("user_type"); //用户身份学生还是老师
  let classStatus = 1; //教室状态
  let roundNum = 0; //当前轮次
  let isPlaying = false;
  //判断是否使用默认背景图片
  let isFirst = true;
  if (configData.bg == "") {
    $(".container").css({
      "background-image": "url(./image/bg.jpg)",
    });
  }
  staticData.dialogBg &&
    $("#dialogBg").css("background-image", `url("${staticData.dialogBg}")`);
  staticData.dialogBtnActiveR &&
    $(".next-btn")
      .first()
      .css("background-image", `url("${staticData.dialogBtnActiveR}")`);
  staticData.dialogBtnActiveL &&
    $(".prev-btn")
      .first()
      .css("background-image", `url("${staticData.dialogBtnActiveL}")`);

  SDK.reportTrackData({
    action: "PG_FT_INTERACTION_LIST",
    data: {
      item: gameList.length || 0,
    },
    teaData: {
      teacher_type: TEACHER_TYPE.TEACHING_INPUT,
      interaction_type: INTERACTION_TYPE.CLICK,
      useraction_type: USERACTION_TYPE.LISTEN,
    },
  });

  let timer = null; //长时间回答不正确时 正确选项出现提示

  // let soundBlasterAnimations = 0;
  let soundBlasterAnimationsObj = [];
  let optionLiAnimationsObj = [];
  let optionLiw = [],
    optionLih = [];
  let fingerIndex = 0;
  let optionClickStus = true; //点击选项事件
  let isVideo;

  let currentClickOptionLiIndex = 0;
  let finished = false;

  /**
   * 初始化
   */

  //生成像素网格
  renderPx();
  //小手位置初始化
  // initHand();
  //初始化标志物
  // initLandMark();
  //判断用户角色，显示不同功能
  isShowBtn();
  //游戏初始化
  initGame();

  //进度条初始化
  // initProgressBar();

  //弹窗
  const carouselInner = $(".carousel-inner");
  const titleCarousel = $(".title-carousel-inner");

  let currentIndex = 0;
  let contentUrlAnimations = 0;
  if (userType == "tea" || !isSync) {
    $(".modal-close").show();
  } else {
    $(".modal-close").hide();
  }

  // 关闭弹窗

  $(".modal-close").on("click touchstart", function (e) {
    SDK.reportTrackData(
      {
        action: "CK_FT_INTERACTION_CLOSEBUTTON",
        data: {},
      },
      USER_TYPE.TEA
    );
    const videos = carouselInner.find("video");
    if (videos.length > 0) {
      videos.each(function () {
        this.pause();
      });
    }

    if (!isSync) {
      $(this).trigger("syncModalClick");
      return;
    }
    SDK.bindSyncEvt({
      sendUser: "",
      receiveUser: "",
      index: $(e.currentTarget).data("syncactions"), //选项的data-syncactions值
      eventType: "click",
      method: "event",
      syncName: "syncModalClick",
      otherInfor: {
        roundNum: roundNum,
      },
      recoveryMode: "1",
    });
    carouselInner.empty();
    titleCarousel.empty();
  });
  //点击弹窗
  $(".modal-close").on("syncModalClick", function (e, message) {
    if (e.type == "touchstart") {
      e.preventDefault();
    }
    e.stopPropagation(); //阻止事件进一步传播
    //销毁元素
    $(".title-img").remove();
    $(".frequencyAudio").remove(); // 同时清理音频元素
    // controlAuth(12);
    $("#carousel-modal").hide();
    currentIndex = 0;
    SDK.setEventLock();
  });
  //点击弹窗
  $(".modal-close").on("click", function (event, message) {
    event.stopPropagation(); // 阻止事件传播
    event.preventDefault();
    SDK.setEventLock();
  });

  // 更新按钮状态
  function updateButtons(data) {
    console.log(
      "modleListmodleListmodleListmodleList",
      data.length,
      data.length == 1
    );
    if (userType == "stu") {
      $(".prev-btn").prop("disabled", true);
      $(".prev-btn").addClass("prev-btn-disabled");
      $(".next-btn").prop("disabled", true);
      $(".next-btn").addClass("next-btn-disabled");
      if (data.length == 1) {
        // 如果列表只有一个元素，禁用左右按钮
        $(".prev-btn").hide();
        $(".next-btn").hide();
      }
      return;
    }
    if (data.length == 1) {
      // 如果列表只有一个元素，禁用左右按钮
      $(".prev-btn").hide();
      $(".next-btn").hide();
    } else {
      $(".prev-btn").show();
      $(".next-btn").show();
      console.log("modleListmodleListmodleListmodleList", currentIndex);
      // 否则根据 currentIndex 设置按钮状态
      if (currentIndex === 0) {
        // $(".prev-btn").prop("disabled", true);
        $(".prev-btn").addClass("prev-btn-disabled");
      } else {
        // $(".prev-btn").prop("disabled", false);
        $(".prev-btn").removeClass("prev-btn-disabled");
      }

      if (currentIndex === data.length - 1) {
        // $(".next-btn").prop("disabled", true);
        $(".next-btn").addClass("next-btn-disabled");
      } else {
        // $(".next-btn").prop("disabled", false);
        $(".next-btn").removeClass("next-btn-disabled");
      }
    }
  }

  // 切换到指定索引的图片
  function goToIndex(index, isIndex) {
    setTimeout(function () {
      checkAndClickPlayButton(index);
    }, 800);
    currentIndex = index;
    const translateX = -currentIndex * 100 + "%";
    carouselInner.css("transform", `translateX(${translateX})`);
    titleCarousel.css("transform", `translateX(${translateX})`);

    // 处理mode==2时的元素显示/隐藏
    if (modleList && modleList.length > 0) {
      // 隐藏所有的title-img元素
      $(".title-img").hide();
      // 仅显示当前索引对应的title-img元素
      $(`.title-img[data-index="${index}"]`).show();
    }

    if (isIndex) {
      updateButtons(modleList);
    }
  }

  // 上一张按钮点击事件
  $(".prev-btn").on("click touchstart", function (e) {
    if (e.type == "touchstart") {
      e.preventDefault();
    }
    e.stopPropagation();
    SDK.reportTrackData(
      {
        action: "CK_FT_INTERACTION_TOAST",
        data: {
          toast: "left",
        },
      },
      USER_TYPE.TEA
    );
    if (!isSync) {
      $(this).trigger("syncPrevBtnClick");
      return;
    }
    SDK.bindSyncEvt({
      sendUser: "",
      receiveUser: "",
      index: $(e.currentTarget).data("syncactions"),
      eventType: "click",
      method: "event",
      syncName: "syncPrevBtnClick",
      otherInfor: {},
      recoveryMode: "1",
    });

    console.log("prev-btn", $(e.currentTarget).data("syncactions"));
  });
  $(".prev-btn").on("syncPrevBtnClick", function (event) {
    console.log("prev-btn syncPrevBtnClick");
    event.stopPropagation(); // 阻止事件传播

    if (currentIndex > 0) {
      goToIndex(currentIndex - 1, true);
      lottPlay(modleList);
      isPlaying = false;
    }
    SDK.setEventLock();
  });

  // 下一张按钮点击事件
  $(".next-btn").on("click touchstart", function (e) {
    if (e.type == "touchstart") {
      e.preventDefault();
    }
    e.stopPropagation();
    SDK.reportTrackData(
      {
        action: "CK_FT_INTERACTION_TOAST",
        data: {
          toast: "right",
        },
      },
      USER_TYPE.TEA
    );
    if (!isSync) {
      $(this).trigger("syncNextBtnClick");
      return;
    }
    SDK.bindSyncEvt({
      sendUser: "",
      receiveUser: "",
      index: $(e.currentTarget).data("syncactions"),
      eventType: "click",
      method: "event",
      syncName: "syncNextBtnClick",
      otherInfor: {},
      recoveryMode: "1",
    });
    console.log("next-btn");
  });
  $(".next-btn").on("syncNextBtnClick", function (event) {
    console.log("next-btn syncNextBtnClick");
    event.stopPropagation(); // 阻止事件传播
    if (currentIndex < modleList.length - 1) {
      if (currentClickOptionLiIndex) {
        gameList[currentClickOptionLiIndex - 1].finishCount++;
      }
      dealSuccessRequest();
      goToIndex(currentIndex + 1, true);
      lottPlay(modleList);
      isPlaying = false;
    }
    SDK.setEventLock();
  });
  // 定义一个函数，检查指定索引的 carousel-item 是否有 play-button play
  function checkAndClickPlayButton(index) {
    const carouselItem = $(".carousel-item").eq(index);
    const playButton = carouselItem.find(".play-button.play");
    if (playButton.length > 0) {
      // 模拟点击
      playButton.trigger("playButtonClick");
      console.log("Play button clicked in carousel-item at index:", index);
    } else {
      console.log("No play button found in carousel-item at index:", index);
    }
  }

  //小手曲线动画
  function handAnima(item) {
    // 检查是否已存在小手元素
    if (!item.find(".handsss").length) {
      // 获取当前项目的轮次
      const roundNum = item.attr("roundNum");
      // 添加小手元素并设置轮次标识
      item.append('<div class="handsss" data-round="' + roundNum + '"></div>');
    }
  }

  //生成像素网格
  function renderPx() {
    let liHtml = "";
    for (let i = 1; i < 801; i++) {
      liHtml += `
                <li class="pos_${i}"></li>	`;
    }
    $(".boxUl").html(liHtml);
  }

  /**
   * 游戏选项初始化
   * @param {*} param_roundNum   轮次
   */

  function initGame(param_roundNum) {
    // $(".optionUl").html("");
    optionLiAnimationsObj = Array.from(
      { length: gameList.length },
      (_, index) => index
    );
    //记忆id图片
    //记忆id memoryData.selectedArr  ['3','1']
    //图片 imgResources  [{id: '1', img: 'xxx'},{id: '3', img: 'xxx'},{id: '2', img: 'xxx'}]
    //位置 positionArr  [{x: 100, y: 200},{x: 300, y: 200},{x: 400, y: 200}],


    gameList.forEach(async function (item, index) {
      //正确图片位置
      let liEle = "";
      let left = item.rightPositionStartX / 100 + "rem";
      let top = item.rightPositionStartY / 100 + "rem";

      // 获取文件扩展名
      const fileExtension = item.rightImg.split('.').pop().toLowerCase();

      if (fileExtension === 'json') {
        // 如果是JSON文件，使用lottie动画
        liEle = $(
          `<li class="optionLi optionLiPic " id="optionLi-lottie-animations${index}"></li>`
        );
      } else {
        // 如果是图片文件，直接设置背景图
        liEle = $(
          `<li class="optionLi optionLiPic"></li>`
        );
      }

      liEle.attr({
        roundNum: index + 1,
        "data-syncactions": "roundNum" + (index + 1),
      });
      var z = 10;
      if (param_roundNum && index + 1 <= param_roundNum) {
        //断线重连  已完成的轮 不显示
        z = -1;
      }
      liEle.css({
        left: left,
        top: top,
        "z-index": z,
      });

      // 如果是图片文件，设置背景图
      if (fileExtension !== 'json') {
        liEle.css({
          "background-image": "url(" + item.rightImg + ")",
        });
      }

      //遮罩图片位置
      let liEle2 = "";
      let left2 = item.shadePositionX / 100 + "rem";
      let top2 = item.shadePositionY / 100 + "rem";
      liEle2 = $('<li  class="optionLi optionLiShade"></li>');
      liEle2.attr({
        roundNum: index + 1,
        "data-syncactions": "shadeRoundNum" + (index + 1),
      });
      liEle2.css({
        left: left2,
        top: top2,
        "background-image": "url(" + item.shadeImg + ")",
      });
      //加载图片 获取宽高
      var preList = [];
      if (fileExtension !== 'json') {
        // 如果是图片，添加到预加载列表
        preList.push({
          image: item.rightImg,
        });
      }
      preList.push({
        image: item.shadeImg,
      });

      var preImgs = [];
      $.when(preloadImg(preList, preImgs)).done(
        async function () {
          $(".optionUl").append(liEle);

          if (fileExtension !== 'json') {
            // 如果是图片，设置图片样式
            liEle.css({
              backgroundSize: "100% 100%",
              width: preImgs[0].width / 100 + "rem",
              height: preImgs[0].height / 100 + "rem",
              "background-image": "url(" + item.rightImg + ")",
            });
            // 遮罩图片样式设置
            liEle2.css({
              "z-index": 20,
              backgroundSize: "100% 100%",
              width: preImgs[1].width / 100 + "rem",
              height: preImgs[1].height / 100 + "rem",
            });
          } else {
            // 如果是JSON，遮罩图片样式设置
            liEle2.css({
              "z-index": 20,
              backgroundSize: "100% 100%",
              width: preImgs[0].width / 100 + "rem",
              height: preImgs[0].height / 100 + "rem",
            });
          }
          $(".optionUl").append(liEle2);
        }
      );
      // 小手引导出现
      setTimeout(async () => {
        if (item.hasHandAnimation === "1") {
          // 先创建小手元素
          // 然后设置样式并显示
          if (item.handPositionX || item.handPositionY) {
            //创建小手元素 添加到container 设置样式
            const handEle = $('<div class="handsss"></div>');
            $(".container").append(handEle);
            console.log(handEle[0]);
            handEle
              .css({
                left: (item.handPositionX ? item.handPositionX / 100 : 0) + "rem",
                top: (item.handPositionY ? item.handPositionY / 100 : 0) + "rem",
              })
              .attr("data-round", index + 1)
              .show();
          } else {
            if (item.shadeImg) {
              handAnima(liEle2);
              liEle2.find(".handsss")
              .css({
                top: "50%",
                left: "50%",
                transform: "translate(-50%, -50%)"
              })
              .show();
            }
          }
        }
      }, 300);
      setTimeout(async () => {
        //最后一轮加载完成后，即所有内容加载完毕后 添加事件
        if (index + 1 == gameList.length) {
          console.log("添加事件");
          addEvent(); //选项添加事件
        }
        // 只有JSON文件才初始化lottie动画
        const elementId = `#optionLi-lottie-animations${index}`;
        if (fileExtension === 'json' && $(elementId).length > 0) {
          optionLiAnimationsObj[index] = await lottieAnimations.init(
            optionLiAnimationsObj[index],
            item.rightImg,
            `#optionLi-lottie-animations${index}`
          );
          lottieAnimations.play(optionLiAnimationsObj[index]);
        }
      }, 300);
    });
  }

  //判断用户角色，显示不同功能(如老师的底部文字提示，学生的预习模式等)
  function isShowBtn() {
    if (isSync) {
      //同步模式
      classStatus = SDK.getClassConf().h5Course.classStatus;
      if (classStatus == 0 && userType == "stu") {
        $(".funcMask").show(); //预习模式和倒计时
      }
    } else {
      //非同步模式
      var hrefParam = parseURL("http://www.example.com");
      if (top.frames[0] && top.frames[0].frameElement) {
        hrefParam = parseURL(top.frames[0].frameElement.src);
      } else {
        hrefParam = parseURL(window.location.href);
      }
      var role_num = hrefParam.params["role"];

      function parseURL(url) {
        var a = document.createElement("a");
        a.href = url;
        return {
          source: url,
          protocol: a.protocol.replace(":", ""),
          host: a.hostname,
          port: a.port,
          query: a.search,
          params: (function () {
            var ret = {},
              seg = a.search.replace(/^\?/, "").split("&"),
              len = seg.length,
              i = 0,
              s;
            for (; i < len; i++) {
              if (!seg[i]) {
                continue;
              }
              s = seg[i].split("=");
              ret[s[0]] = s[1];
            }
            return ret;
          })(),
          file: (a.pathname.match(/\/([^\/?#]+)$/i) || [, ""])[1],
          hash: a.hash.replace("#", ""),
          path: a.pathname.replace(/^([^\/])/, "/$1"),
          relative: (a.href.match(/tps?:\/\/[^\/]+(.+)/) || [, ""])[1],
          segments: a.pathname.replace(/^\//, "").split("/"),
        };
      }
      if (role_num == "1" || role_num == undefined) {
        $(".funcMask").show();
      } else if (role_num == "2") {
        $(".funcMask").show();
      }
    }
  }

  /**
   *  开始游戏
   *  2种途径可开启游戏，1.老师通过控制器点击开始 2.非课中模式，如预习，学生点击开启
   */
  //1.老师通过控制器点击开始
  window.SDK.actAuthorize = function (message) {
    if (isSync) {
      if (userType == "tea" && SDK.getClassConf().h5Course.classStatus == 5) {
        //老师显示下方提示条
        $(".doneTip").removeClass("hide");
      }

      if (message && message.operate == 5) {
        isFirst = false;
      }
      if (message && message.type == "practiceStart") {
        //第一次授权才显示倒计时
        if (isFirst) {
          isFirst = false;
          // $(".funcMask").show();
          // $(".optionUl").html("");
          // threeTwoOne(); //321倒计时
        }
      }
    }
  };
  //2.非课中模式，如预习，学生点击开启
  let startBtnStatus = true; //开始按钮是否可点击(预习模式)
  $(".startBtn").on("click touchstart", function (e) {
    if (e.type == "touchstart") {
      e.preventDefault();
    }
    e.stopPropagation();
    if (startBtnStatus) {
      startBtnStatus = false;
      // threeTwoOne(); //321倒计时
    }
  });

  /**
   *  demo模式
   *  2种途径可开启demo模式，1.老师通过控制器点击提示 2.非课中模式，如预习，学生点击开启
   */
  //1.老师通过控制器点击提示
  window.SDK.actDemo = function (message) {
    // demoFun(false); //demo模式
    //老师上传一份空白cldata,来覆盖以前的数据。
    if (isSync) {
      SDK.bindSyncEvt({
        index: "clear",
        eventType: "click",
        method: "event",
        syncName: "clear", //SDK.js中对其特殊处理
        recoveryMode: "1",
      });
    }
    SDK.setEventLock();
  };
  //2.非课中模式，如预习，学生点击开启
  $(".demo-btnStu").on("click touchstart", function (e) {
    if (e.type == "touchstart") {
      e.preventDefault();
    }
    e.stopPropagation();
    // demoFun(true); //demo模式
  });

  /**
   * 开启新一轮（控制轮次和音频）
   * from <EMAIL>
   */
  function gameControl() {
    $(".optionLiPic").removeClass("optionScale");
    if (roundNum >= gameList.length) {
      //游戏已结束
      clearTimeout(timer); //清除计时
      // soundControl().pause();
      if (userType == "stu" || !isSync) {
        // sendGameOver(); //游戏结束
      }
      return;
    }

    roundNum = roundNum + 1; //第一轮

    optionClickStus = true;
  }

  function dealSuccessRequest() {
    for (let i = 0; i < gameList.length; i++) {
      if (gameList[i].finishCount !== gameList[i].modalList.length) {
        return;
      }
      if (!finished && i === gameList.length - 1) {
        SDK.reportTrackData(
          {
            action: "CK_FT_INTERACTION_COMPLETE",
            data: {
              result: "success",
            },
          },
          USER_TYPE.TEA
        );
        finished = true;
      }
    }
  }

  //选项添加事件
  function addEvent() {
    //选项
    $(".optionLi").on("click touchstart", function (e) {
      const userType = window.frameElement
        ? window.frameElement.getAttribute("user_type")
        : "";
      let self = $(this);
      let round = self.attr("roundNum");
      SDK.reportTrackData(
        {
          action: "CK_FT_INTERACTION_ITEM",
          data: {
            item: round,
          },
        },
        userType === "stu" ? USER_TYPE.STU : USER_TYPE.TEA
      );
      if (e.type == "touchstart") {
        e.preventDefault();
      }
      e.stopPropagation(); //阻止事件进一步传播

      if (!isSync) {
        $(this).trigger("syncItemClick");
        return;
      }
      SDK.bindSyncEvt({
        sendUser: "",
        receiveUser: "",
        index: $(e.currentTarget).data("syncactions"), //选项的data-syncactions值
        eventType: "click",
        method: "event",
        syncName: "syncItemClick",
        otherInfor: {
          roundNum: roundNum,
        },
        recoveryMode: "1",
      });
    });
    //点击选项
    $(".optionLi").on("syncItemClick", function (e, message) {
      // 获取当前点击的选项元素
      let self = $(this);
      // 获取当前选项的轮次编号
      let roundNumc = self.attr("roundNum");
      // 记录当前点击的选项索引，用于后续功能如播放音频和视频时的标识
      currentClickOptionLiIndex = roundNumc;
      // 检查该轮次是否有完成记录，如果没有则初始化为1
      if (!gameList[roundNumc - 1].finishCount) {
        gameList[roundNumc - 1].finishCount = 1;
      }
      // 检查是否所有任务都已完成，如完成则会触发相应的成功上报
      dealSuccessRequest();

      console.log("self======>", self.attr("class"));
      // 隐藏与该轮次相关的手型动画指示器
      $(".handsss[data-round='" + roundNumc + "']").hide();
      // 判断点击的是否为遮罩元素，如是则将操作对象改为前一个元素（即实际的选项图片）
      if (self.hasClass("optionLiShade")) {
        //如果点击的是选项的遮罩  目标改为选项图片
        self = self.prev();
      }
      //根据clickMaskStatus 判断 是否隐藏遮罩图
      const currentRoundIndex = roundNumc - 1;
      const clickMaskStatus = gameList[currentRoundIndex].clickMaskStatus;
      // 如果clickMaskStatus为"1"，则隐藏遮罩图
      if (clickMaskStatus === "1") {
        // 查找对应轮次的遮罩图并隐藏
        $(`.optionLiShade[roundNum="${roundNumc}"]`).hide();
      }

      // 执行游戏控制逻辑，可能会更新轮次、启用选项点击等
      gameControl();
      // 从处理后的选项元素中再次获取轮次编号
      let roundNumInpic = self.attr("roundNum");

      // 触发正确答案的视觉反馈效果
      rightWrong(self).right(); //点击正确
      // 计算数组索引（轮次编号减1）
      let indexSet = roundNumInpic - 1;
      // 检查当前轮次是否有模态内容列表需要显示
      if (gameList && gameList[indexSet] && gameList[indexSet].modalList) {
        // 设置全局模态列表变量
        modleList = gameList[indexSet].modalList;
        // 设置事件锁，防止重复操作
        SDK.setEventLock();
        console.log("modleList======>length", modleList.length);
        // 如果模态列表为空，则直接返回不执行后续操作
        if (modleList.length == 0) return;
        // 根据模态列表内容更新导航按钮状态（上一个/下一个）
        updateButtons(modleList);
        // 显示模态内容，可能包含图片、视频或JSON动画，并使用指定模式
        sliderModle(modleList, gameList[indexSet].modalMode);
        // lottPlay(modleList) // 注释掉的音频播放功能
        // 显示轮播模态框
        $("#carousel-modal").show();
        // 根据showMask的值，设置模态框的背景色
        if (gameList[indexSet].showMask == "2") {
          //清除元素背景色
          $("#carousel-modal").css("background-color", "transparent");
        } else {
          $("#carousel-modal").css("background-color", "");
        }
        // controlAuth(11);
      }
      console.log("点击======》setEventLock");
      // 再次设置事件锁，确保操作完成
      SDK.setEventLock();
      console.log("点击======》setEventLock");
      // 移除选项的抖动效果类
      self.removeClass("shake");
      // 重置选项点击状态为可点击
      optionClickStus = true;

      // 如果是学生用户，则上报游戏进度数据用于同步
      if (userType == "stu") {
        gameProcessBidSync(); //上报游戏数据
      }
    });
  }
  async function lottPlay(data) {
    console.log("data======> lottPlay", data);
    if (data[currentIndex].modalAudio !== "") {
      // 使用jQuery的检测方法
      const checkExist = setInterval(async () => {
        const audioElement = $(`.frequencyAudio${currentIndex}`)[0];
        if (audioElement) {
          clearInterval(checkExist);

          // 音频播放逻辑
          audioElement.currentTime = 0;

          // 播放音频上报
          SDK.reportTrackData(
            {
              action: "CK_FT_INTERACTION_AUDIOPLAY",
              data: {
                audio: currentClickOptionLiIndex,
              },
            },
            USER_TYPE.TEA
          );

          SDK.playRudio({
            index: audioElement,
            syncName: $(`.frequencyAudio${currentIndex}`).attr("data-syncaudio"),
          });

          audioElement.onended = function () {
            console.log("音频结束");
            lottieAnimations.stop(soundBlasterAnimationsObj[currentIndex]);
          }.bind(this);
          let audioHorn = "./image/laba.json";
          if (!soundBlasterAnimationsObj[currentIndex]) {
            soundBlasterAnimationsObj[currentIndex] = await lottieAnimations.init(
              soundBlasterAnimationsObj[currentIndex],
              audioHorn,
              `.sound-blaster${currentIndex}`
            );
          }

          lottieAnimations.play(soundBlasterAnimationsObj[currentIndex]);

        }
      }, 100);

      // 设置超时保护
      setTimeout(() => clearInterval(checkExist), 3000);

      // 动画初始化和播放逻辑

    }
  }

  // 动态生成图片项
 function sliderModle(data, mode = "1") {
    carouselInner.empty();
    titleCarousel.empty();

    mode == "2"
      ? $(".modal-content").addClass("full")
      : $(".modal-content").removeClass("full");

    goToIndex(0, false);
    // 根据 data 数组的长度初始化 soundBlasterAnimationsObj 数组
    soundBlasterAnimationsObj = new Array(data.length).fill(0);
    data.forEach(async (imageSrc, index) => {
      const contentUrl = imageSrc.content;
      const fileExtension = contentUrl.split(".").pop().toLowerCase();
      const item = $(`<div class="carousel-item"></div>`);
      let titleItem = null;
      if (mode == "1") {
        titleItem = $(
          `<div class="carousel-item-title">
          <p class="sound-blaster${index} sound-blaster" id="activItme" data-syncactions="carouselActions${index}"></p>
          </div>`
        );
        const titleImg = imageSrc.title
          ? $("<img>")
              .attr("src", imageSrc.title)
              .attr("data-syncactions", `imgTitle${index}`)
          : "<div class='no-img'></div>";

        const audioConter = $(
          `<audio class="frequencyAudio frequencyAudio${index}" data-syncaudio="frequencyAudio${index}" src='${imageSrc.modalAudio}'></audio>`
        );

        // 检测是否audioConter 有音频 如果有音频根据currentIndex 播放相应的音频
        titleItem.find("#activItme").on("click touchstart", (e) => {
          const currentElement = $(e.currentTarget);
          if (e.type == "touchstart") {
            e.preventDefault();
          }
          e.stopPropagation();
          console.log("点击播放按钮", $(e.currentTarget));
          if (!isSync) {
            $("#activItme").trigger("activItmeClick");
            return;
          }
          SDK.bindSyncEvt({
            sendUser: "",
            receiveUser: "",
            index: $(e.currentTarget).data("syncactions"),
            eventType: "click",
            method: "event",
            syncName: "activItmeClick",
            otherInfor: {},
            recoveryMode: "1",
          });
        });

        titleItem.append(titleImg, audioConter);
      }
            if (mode == "2") {
        // 检查是否有title图片
        if (imageSrc.title) {
          //获取图片宽高 imageSrc
          getImageSize(imageSrc.title, (width, height) => {
            // 设置内容图片的位置和尺寸
            const contentContainer = $(
              '<div class="title-img"></div>'
            );

            // 添加data-index属性用于定位元素
            contentContainer.attr('data-index', index);

            // 如果不是第一个元素，则初始隐藏
            if (index !== 0) {
              contentContainer.hide();
            }

            contentContainer.css({
              position: "absolute",
              zIndex: 99,
              width: width / 100 + "rem",
              height: height / 100 + "rem",
              left: imageSrc.titlePositionX / 100 + "rem",
              top: imageSrc.titlePositionY / 100 + "rem",
              backgroundImage: `url(${imageSrc.title})`,
              backgroundPosition: "center",
              backgroundRepeat: "no-repeat",
              backgroundSize: "contain",
            });

            let p =
              $(`<p class="sound-blaster${index}" id="activItme" data-syncactions="carouselActions${index}">
              </p>`);
            p.css({
              transform: "translate(-50%, -50%)",
              cursor: "pointer",
              width: "1.08rem",
              height: "1.08rem",
            });
            contentContainer.append(p);

            const audioConter = $(
              `<audio class="frequencyAudio frequencyAudio${index}" data-syncaudio="frequencyAudio${index}" src='${imageSrc.modalAudio}'></audio>`
            );
            $(".full").append(contentContainer, audioConter);

            // 音频播放按钮事件处理 - 移到这里，确保contentContainer已定义
            contentContainer.find("#activItme").on("click touchstart", (e) => {
              const currentElement = $(e.currentTarget);
              if (e.type == "touchstart") {
                e.preventDefault();
              }
              e.stopPropagation();
              console.log("点击播放按钮", $(e.currentTarget));
              if (!isSync) {
                $("#activItme").trigger("activItmeClick");
                return;
              }
              SDK.bindSyncEvt({
                sendUser: "",
                receiveUser: "",
                index: $(e.currentTarget).data("syncactions"),
                eventType: "click",
                method: "event",
                syncName: "activItmeClick",
                otherInfor: {},
                recoveryMode: "1",
              });
            });
          });
        } else if (imageSrc.modalAudio) {
          // 如果没有title图片但有音频，只创建音频元素
          const audioConter = $(
            `<audio class="frequencyAudio frequencyAudio${index}" data-syncaudio="frequencyAudio${index}" src='${imageSrc.modalAudio}'></audio>`
          );
          $(".full").append(audioConter);
          }
        }

      if (
        fileExtension === "jpg" ||
        fileExtension === "jpeg" ||
        fileExtension === "png" ||
        fileExtension === "gif"
      ) {
        console.log("soundBlasterAnimationsObj===》图片");
        // 图片
        const img = $("<img>")
          .attr("src", contentUrl)
          .attr("data-syncactions", `imgContent${index}`);
        if (mode == "2") {
          getImageSize(contentUrl, (width, height) => {
            img.css({
              position: "absolute",
              zIndex: 4,
              width: width / 100 + "rem",
              height: height / 100 + "rem",
              top: "50%",
              left: "50%",
              transform: "translate(-50%,-50%)",
            });
          });
        }
        item.append(img);
      } else if (
        fileExtension === "mp4" ||
        fileExtension === "webm" ||
        fileExtension === "ogg"
      ) {
        // 视频
        const videoContainer = $('<div class="custom-video-container"></div>');
        const video = $("<video playsinline><p ></p></video>").attr(
          "src",
          contentUrl
        );
        const playButton = $(
          `<div class="play-button play" data-syncactions='video${index}'></div>`
        );
        const progressBar = $(
          '<div class="progress-box"><span class="current-time">00:00</span><span class="current-timesDats"> / </span><span class="total-time">00:00</span><div class="progress-bar"><div class="progress"></div></div></div>'
        );
        // 根据videoFirstFrame字段决定预览图
        const videoPreviewSrc = imageSrc.videoFirstFrame ? imageSrc.videoFirstFrame : './image/custom-bg.jpg';
        const customControls = $(
          `<div class="custom-controls"><img class="video-icon" src="${videoPreviewSrc}"></div>`
        );
        // videoContainer.append(playButton);
        customControls.append(progressBar, playButton);
        videoContainer.append(video, customControls);
        item.append(videoContainer);
        // 确保视频元数据加载完成后再获取 duration
        video.on("loadedmetadata", () => {
          const totalTime = formatTime(video[0].duration);

          $(".total-time").text(totalTime);
        });
        playButton.on("click touchstart", (e) => {
          // // todo 播放视频
          // console.log('播放时极品')
          if (userType === "stu") return;

          if (e.type == "touchstart") {
            e.preventDefault();
          }
          e.stopPropagation();
          console.log("点击播放按钮", $(e.currentTarget));
          if (!isSync) {
            playButton.trigger("playButtonClick");
            return;
          }
          SDK.bindSyncEvt({
            sendUser: "",
            receiveUser: "",
            index: $(e.currentTarget).data("syncactions"),
            eventType: "click",
            method: "event",
            syncName: "playButtonClick",
            otherInfor: {},
            recoveryMode: "1",
          });
        });
        // 初始化播放按钮和进度条
        isPlaying = false;
        playButton.on("playButtonClick", (event) => {
          event.stopPropagation(); // 阻止事件传播
          if (isPlaying) {
            video[0].pause();
            playButton.removeClass("pause").addClass("play");
            $(event.target).parent().find(".progress-box").show();
          } else {
            SDK.reportTrackData(
              {
                action: "CK_FT_INTERACTION_VIDIOPLAY",
                data: {
                  video: currentClickOptionLiIndex,
                },
              },
              USER_TYPE.TEA
            );
            video[0].play();
            $(event.target).parent().find(".video-icon").hide();
            playButton.removeClass("play");
            $(event.target).parent().find(".progress-box").hide();
          }
          isPlaying = !isPlaying;
          SDK.setEventLock();
        });

        video.on("timeupdate", (event) => {
          const progress = (video[0].currentTime / video[0].duration) * 100;
          $(event.target)
            .parent()
            .find(".progress")
            .css("width", progress + "%");
          // 更新当前时间和总时间
          const currentTime = formatTime(video[0].currentTime);
          $(event.target).parent().find(".current-time").text(currentTime);
        });
        video.on("ended", (event) => {
          // 播放完成后的处理逻辑
          playButton.removeClass("pause").addClass("play");
          $(event.target).parent().find(".progress-box").show();
          isPlaying = false;
        });

        progressBar.click((event) => {
          const rect = progressBar[0].getBoundingClientRect();
          const x = event.clientX - rect.left;
          const newTime = (x / progressBar.width()) * video[0].duration;
          video[0].currentTime = newTime;
        });
      } else if (fileExtension === "json") {
        // JSON 文件
        const json = $(
          `<div class="custom-contentUrl-container custom-contentUrl-container${index}"></div>`
        );
        item.append(json);
        // 初始化 Lottie 动画
        setTimeout(async () => {
          contentUrlAnimations = await lottieAnimations.init(
            contentUrlAnimations,
            contentUrl,
            `.custom-contentUrl-container${index}`
          );
          lottieAnimations.play(contentUrlAnimations);
        }, 300);

        console.log("contentUrlcontentUrl", contentUrl);
      } else {
        // 其他文件类型
        const unknownText = $("<p>未知文件类型: " + contentUrl + "</p>");
        item.append(unknownText);
      }

      carouselInner.append(item);
      if (mode == "1") {
        titleCarousel.append(titleItem);
      }
      // debugger
      // if(mode == '2') {
      //   $('.modal').append(item)
      // }
    });
    lottPlay(data);
  }
  $(document).on("activItmeClick", "#activItme", function (event) {
    event.stopPropagation(); // 阻止事件传播
    console.log("自定义·事件activItmeClick触发了");
    let audioElement = $(".frequencyAudio").get(currentIndex);
    audioElement.currentTime = 0;
    SDK.playRudio({
      index: audioElement,
      syncName: $(".frequencyAudio" + currentIndex).attr("data-syncaudio"),
    });
    lottieAnimations.play(soundBlasterAnimationsObj[currentIndex]);

    audioElement.onended = function () {
      console.log("音频结束");
      lottieAnimations.stop(soundBlasterAnimationsObj[currentIndex]);
    }.bind(this);
    SDK.setEventLock();
  }
  );
  // 格式化时间
  function formatTime(time) {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes
      .toString()
      .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
  }

  //上报游戏数据方便重连（切换下一轮、点击选项）
  function gameProcessBidSync() {
    if (isSync) {
      SDK.bindSyncEvt({
        index: "runing",
        eventType: "click",
        method: "event",
        syncName: "syncRuning",
        otherInfor: {
          roundNum: roundNum, //第几轮
        },
        recoveryMode: "1",
      });
    }
  }

  //游戏中恢复数据
  $(".runing").on("syncRuning", function (e, message) {
    if (isSync) {
      let obj = message.data[0].value.syncAction.otherInfor;
      if (message.operate == 5) {
        //直接恢复页面
        rebackPage().whenClick(obj);
      }
    }
    SDK.setEventLock();
  });

  //结束游戏 发送数据(由学生判断游戏结束，老师只接受学生的结果)
  function sendGameOver() {
    if (!isSync) {
      $(".overBtn").trigger("gameOver");
    } else {
      SDK.bindSyncEvt({
        index: "overBtn",
        eventType: "mygameevent",
        method: "event",
        syncName: "gameOver",
        otherInfor: {
          roundNum: roundNum,
        },
        recoveryMode: "1",
      });
    }
  }

  //结束反馈事件
  $(".overBtn").on("gameOver", function (e, message) {
    clearTimeout(timer); //答对清除计时
    $(".optionLiPic").removeClass("optionScale"); //移除无人答题时选项放大的动画
    if (isSync) {
      let obj = message.data[0].value.syncAction.otherInfor;
      if (message.operate == "5") {
        endGameappendImg("whenend");
        rebackPage().whenEnd(obj);
        return;
      } else {
        endGameappendImg("over");
      }
      //如果是老师 告知控制器，将授权状态改为老师控制，classstatus为6
      if (
        userType == "tea" &&
        (SDK.getClassConf().h5Course.classStatus == "5" ||
          SDK.getClassConf().h5Course.classStatus == "1")
      ) {
        SDK.bindSyncCtrl({
          type: "gameOverToCtrl",
          data: {
            CID: SDK.getClassConf().course.id + "", //教室id 字符串
            operate: "1",
            data: [
              {
                key: "classStatus",
                value: "6",
                ownerUID: SDK.getClassConf().user.id,
              },
            ],
          },
        });
      }
    } else {
      endGameappendImg("over");
    }
    SDK.setEventLock();
  });

  /**
   *   终局页面去重动态插入图片  所有正确选项去重
   *   type  'over'正常结束   'whenend'断线重连结束
   */
  function endGameappendImg(type) {
    //隐藏下方提示条
    $(".doneTip").addClass("hide");

    var time = 1;
    //终局特效
    if (type == "over") {
      //有回答正确的 且不是断线重连
      //播放终局特效
      // resultWin({
      //   WinIsLoop: false, // 答对声音是否重复播放 true/false
      //   Mask_Z_Index: "500", // 遮罩层z_index
      // });
      var prefect = $(
        "<div class='perfectBox'><img src='./image/light.png' class='light'><img src='./image/prefect.png' class='perfect'></div>"
      );
      $(".resultWin").append(prefect);
      time = 5000; //5s后 隐藏终局特效
    }
    setTimeout(function () {
      resultHide();
      //显示所有正确选项
      $(".optionLiPic").css({
        "z-index": 30,
      });
      // soundControl().pause();
      $(".endGame").show();
    }, time);
  }

  //掉线页面恢复（点击选项、结束）
  function rebackPage(data) {
    return {
      whenClick: function (data) {
        isFirst = false;
        if (userType == "tea" && SDK.getClassConf().h5Course.classStatus == 5) {
          //下方提示条
          $(".doneTip").removeClass("hide");
        }
        // score = data.score; //分数
        roundNum = data.roundNum; //第几轮

        initGame(roundNum); //本轮游戏选项初始化
        // initLandMark(roundNum); //恢复标志物
        recoverProgressBar(roundNum); //恢复进度条
        addEvent(); //添加事件
        gameControl(); //开启新一轮（控制轮次和音频）
        SDK.setEventLock();
      },
      whenEnd: function (data) {
        isFirst = false;
        $(".doneTip").hide();
        roundNum = data.roundNum;
        recoverProgressBar(roundNum); //恢复进度条
        endGameappendImg("whenEnd"); //结束游戏
        SDK.setEventLock();
      },
    };
  }

  //正确与错误逻辑(停止选项音、播放反馈音，改变分数、自增答对个数)
  function rightWrong(self) {
    // soundControl().pause(); //停止
    return {
      right: function () {
        self.addClass("already-hide");
        rightAni(self); //正确反馈（在正确反馈中开启下一轮）
      },
      wrong: function () {
        self.addClass("shake");
      },
    };
  }

  /**
   * 正确反馈
   * @param  {object} self 需要操作的元素
   * @param  {object}  showfuncMask  是否展示预习遮罩(学生自己预习时展示)；无论true/flase，有值则为demo模式。
   * @returns
   * from <EMAIL>
   */
  function rightAni(self, showfuncMask) {
    // var audioTime = gameList[roundNum - 1].audioTime; //音频时长，若音频时长小于1000毫秒 视为 1000毫秒
    // audioTime = audioTime > 1000 ? audioTime : 1000;
    //目标出现在正确反馈位置 且在遮罩层上方
    const currentElRoundNum = self.attr("roundnum");
    let left = gameList[currentElRoundNum - 1].rightPositionEndX / 100 + "rem";
    let top = gameList[currentElRoundNum - 1].rightPositionEndY / 100 + "rem";
    self.css({
      left: left,
      top: top,
      "z-index": 21,
    });

    //弹窗显示
    let timer1 = setTimeout(async () => {
      // firstRoleAnimations = await lottieAnimations.init(firstRoleAnimations, source.firstRole, ".firstRole");
      //非demo模式下 每轮隐藏选项
      // if (showfuncMask == undefined) {
      self.css({
        left: left,
        top: top,
      });
      // }

      clearInterval(timer1);
      gameControl(); //开启下一轮(进入结束流程)
    }, 1000);
  }

  /**
   * 断线重连-恢复进度条
   * @param
   * @returns
   * from <EMAIL>
   */
  function recoverProgressBar(roundNum) {
    var roundNumAll = gameList.length;
    if (roundNumAll == 1) {
      //仅一轮时
      return;
    } else {
      //大于一轮时
      for (var i = 1; i <= roundNum; i++) {
        //显示黄点
        $(".y" + i).css({
          opacity: 1,
        });
      }
      //延长黄色进度条
      $(".yellowBar").css({
        width: `${0.8 * (roundNum - 1)}rem`,
      });
    }
  }
  /**
   * demo模式
   * @param showfuncMask  是否显示预习模式遮罩
   * @todo 1.如果师生任一断线（收到成员变更协议，out者为师/生），则取消demo模式  2. demo模式下  若再次收到demo模式指令，则重新播放
   */

  /**
   * 退出demo模式,恢复正常模式
   * @todo 1.如果师生任一断线（收到成员变更协议，out者为师/生），则取消demo模式  2. demo模式下  若再次收到demo模式指令，则重新播放
   */
});
/**
 * 预加载图片的方法
 * @param {*} list
 * list示例：  [{
            image: "assets/images/5ef1e222783f07ea41000030.png",
        }, {
            image: "assets/images/5ef1e222783f07ea41000030.png",
        }],
 * @param {*} imgs
 */
function preloadImg(list, imgs) {
  var def = $.Deferred(),
    len = list.length;
  $(list).each(function (i, e) {
    var img = new Image();
    img.src = e.image;
    if (img.complete) {
      imgs[i] = img;
      len--;
      if (len == 0) {
        def.resolve();
      }
    } else {
      img.onload = (function (j) {
        return function () {
          imgs[j] = img;
          len--;
          if (len == 0) {
            def.resolve();
          }
        };
      })(i);
      img.onerror = function () {
        len--;
      };
    }
  });
  return def.promise();
}

function getImageSize(url, callback) {
  const img = new Image();
  img.src = url;
  // 确保图片加载完成后获取宽高
  img.onload = function () {
    const width = img.width;
    const height = img.height;
    callback(width, height);
  };
  // 处理加载错误
  img.onerror = function () {
    console.error("图片加载失败");
  };
}

//权限切换 参数是权限值 11:老师权限 12:学生权限
function controlAuth(authCode) {
  if (!isSync || SDK.getClassConf().h5Course.classStatus == CLASS_STATUS.NOT) {
    return;
  }
  SDK.bindSyncCtrl({
    type: "tplAuthorization",
    tplAuthorization: "tplAuthorization",
    data: {
      CID: SDK.getClassConf().course.id + "", //教室id 字符串
      operate: "1",
      data: [
        {
          key: "classStatus",
          value: authCode,
          ownerUID: SDK.getClassConf().user.id,
        },
      ],
    },
  });
}
